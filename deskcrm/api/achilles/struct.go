package achilles

// LessonBaseInfoResponse 章节基础信息响应
type LessonBaseInfoResponse struct {
	LessonInfo       map[string]LessonInfo    `json:"lessonInfo"`       // 章节信息
	TeacherLesson    map[string]TeacherLesson `json:"teacherLesson"`    // 教师章节关系
	LessonStatusInfo map[string]int           `json:"lessonStatusInfo"` // 章节状态信息
}

// RelatedLessonInfo 关联章节信息
type RelatedLessonInfo struct {
	LessonId    int    `json:"lessonId"`    // 章节ID
	LessonName  string `json:"lessonName"`  // 章节名称
	TeacherName string `json:"teacherName"` // 教师名称
	Subject     string `json:"subject"`     // 学科
	TeacherUid  int    `json:"teacherUid"`  // 教师UID
}

// SecondOutlineInfo 二级大纲信息
type SecondOutlineInfo struct {
	Name                string `json:"name"`                   // 名称
	SecondStartTime     int64  `json:"second_start_time"`      // 开始时间
	SecondStopTime      int64  `json:"second_stop_time"`       // 结束时间
	SecondStartRestTime int64  `json:"second_start_rest_time"` // 休息开始时间
	SecondStopRestTime  int64  `json:"second_stop_rest_time"`  // 休息结束时间
}

// LessonInfo 章节信息
type LessonInfo struct {
	// 基础字段
	LessonId       int               `json:"lessonId"`       // 章节ID
	LessonName     string            `json:"lessonName"`     // 章节名称
	LearnTarget    string            `json:"learnTarget"`    // 学习目标
	StartTime      int64             `json:"startTime"`      // 开始时间
	StopTime       int64             `json:"stopTime"`       // 结束时间
	FinishTime     int64             `json:"finishTime"`     // 结束时间
	Status         int               `json:"status"`         // 状态
	CreateTime     int64             `json:"createTime"`     // 创建时间
	UpdateTime     int64             `json:"updateTime"`     // 更新时间
	OutlineId      int               `json:"outlineId"`      // 大纲ID
	UnLockTime     int64             `json:"unLockTime"`     // 解锁时间
	IsOwnPackage   int               `json:"isOwnPackage"`   // 是否自有包
	Playback       string            `json:"playback"`       // 回放地址
	ExtFlag        int               `json:"extFlag"`        // 扩展标志
	BanxueInfo     interface{}       `json:"banxueInfo"`     // 伴学信息
	Mode           int               `json:"mode"`           // 模式
	OutlineHour    string            `json:"outlineHour"`    // 大纲课时
	Branch         int               `json:"branch"`         // 分支
	CourseId       []int             `json:"courseId"`       // 课程ID列表
	LessonType     int               `json:"lessonType"`     // 章节类型
	PlayType       int               `json:"playType"`       // 播放类型
	RelatedLesson  RelatedLessonInfo `json:"relatedLesson"`  // 关联章节
	Attached       string            `json:"attached"`       // 附件
	PreviewNoteUri string            `json:"previewNoteUri"` // 预习笔记URI
	ClassNoteUri   string            `json:"classNoteUri"`   // 课堂笔记URI
	HasHomework    int               `json:"hasHomework"`    // 是否有作业
	HasPlayback    int               `json:"hasPlayback"`    // 是否有回放
	ReopenLessonId int               `json:"reopenLessonId"` // 重开章节ID

	// 统计字段
	FinishNum     int `json:"finishNum"`     // 完成人数
	AttendNum     int `json:"attendNum"`     // 出席人数
	AttendLongNum int `json:"attendLongNum"` // 长时出席人数
	RegisterNum   int `json:"registerNum"`   // 注册人数
	DeleteTime    int `json:"deleteTime"`    // 删除时间
	DeleteReason  int `json:"deleteReason"`  // 删除原因
	CoverPics     int `json:"coverPics"`     // 封面图片

	// 复杂字段
	StageTest     interface{}         `json:"stageTest"`     // 阶段测试
	FileList      interface{}         `json:"fileList"`      // 文件列表
	ServiceInfo   interface{}         `json:"serviceInfo"`   // 服务信息
	SecondOutline []SecondOutlineInfo `json:"secondOutline"` // 二级大纲
	HasShare      int                 `json:"hasShare"`      // 是否共享
	ShareIdList   interface{}         `json:"shareIdList"`   // 共享ID列表
	IndexLessonId int                 `json:"indexLessonId"` // 索引章节ID
	SubjectId     int                 `json:"subjectId"`     // 学科ID
	VisitType     int                 `json:"visitType"`     // 访问类型
	T007Tag       int                 `json:"t007Tag"`       // T007标签
	T007LessonId  int                 `json:"t007LessonId"`  // T007章节ID
	RoundList     interface{}         `json:"roundList"`     // 轮次列表
	SubLessonList interface{}         `json:"subLessonList"` // 子章节列表

	// 测试和配置字段（根据实际API返回添加）
	Ceshizifu        string      `json:"ceshizifu"`        // 测试字符
	Lvyueshichang    int         `json:"lvyueshichang"`    // 绿约时长
	Ceshilcsb        string      `json:"ceshilcsb"`        // 测试字段
	Ceshiqcsb        string      `json:"ceshiqcsb"`        // 测试字段
	Ceshiqiche       string      `json:"ceshiqiche"`       // 测试字段
	Tengdanxuan      int         `json:"tengdanxuan"`      // 腾单选
	Ceshisg          int         `json:"ceshisg"`          // 测试字段
	Ceshilc          string      `json:"ceshilc"`          // 测试字段
	Ceshijunshi      string      `json:"ceshijunshi"`      // 测试字段
	ConfigTest1      int         `json:"config_test1"`     // 配置测试1
	Xldxstrcs        interface{} `json:"xldxstrcs"`        // 字段
	LessonHour       string      `json:"lessonHour"`       // 章节课时
	Zhengxingceshi   int         `json:"zhengxingceshi"`   // 整形测试
	Tengshurukuang   string      `json:"tengshurukuang"`   // 腾输入框
	Ceshifjsb        string      `json:"ceshifjsb"`        // 测试字段
	IsExempt         int         `json:"isExempt"`         // 是否免修
	XXGJClassType    int         `json:"XXGJClassType"`    // 班型
	Xldx01           interface{} `json:"xldx01"`           // 字段
	Ceshifeiji       string      `json:"ceshifeiji"`       // 测试字段
	Ceshixiancheng   string      `json:"ceshixiancheng"`   // 测试字段
	Ceshitd          int         `json:"ceshitd"`          // 测试字段
	Wbcs             string      `json:"wbcs"`             // 字段
	Tengduoxuankuang interface{} `json:"tengduoxuankuang"` // 腾多选框
	IsSmallClass     int         `json:"isSmallClass"`     // 是否小班
	Ceshishiqu       string      `json:"ceshishiqu"`       // 测试字段
}

// TeacherLesson 教师章节关系
type TeacherLesson struct {
	TeacherUid int `json:"teacherUid"` // 教师UID
}

// ProcessedLessonInfo 处理后的章节信息
type ProcessedLessonInfo struct {
	LessonInfo                 // 匿名嵌入LessonInfo的所有字段，实现字段平铺
	Teacher      TeacherLesson `json:"teacher"`      // 教师信息
	LessonStatus int           `json:"lessonStatus"` // 章节状态
	TeacherUid   int           `json:"teacherUid"`   // 教师UID
	IsClassing   int           `json:"isClassing"`   // 是否正在上课
}
